<?php
/**
 * Login Page
 * Secure login form with CSRF protection
 */

require_once 'config/config.php';
require_once 'classes/User.php';

// Redirect if already logged in
if (isLoggedIn()) {
    header('Location: dashboard.php');
    exit();
}

$error_message = '';
$success_message = '';

// Handle login form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Verify CSRF token
    if (!isset($_POST['csrf_token']) || !verifyCSRFToken($_POST['csrf_token'])) {
        $error_message = 'Invalid request. Please try again.';
    } else {
        $email = sanitizeInput($_POST['email']);
        $password = $_POST['password'];
        
        // Validate input
        if (empty($email) || empty($password)) {
            $error_message = 'Please enter both email and password.';
        } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $error_message = 'Please enter a valid email address.';
        } else {
            // Attempt login
            $database = new Database();
            $db = $database->getConnection();
            $user = new User($db);
            
            if ($user->login($email, $password)) {
                header('Location: dashboard.php');
                exit();
            } else {
                $error_message = 'Invalid email or password.';
                // Log failed login attempt
                error_log("Failed login attempt for email: " . $email . " from IP: " . $_SERVER['REMOTE_ADDR']);
            }
        }
    }
}

// Generate CSRF token
$csrf_token = generateCSRFToken();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Advanced Customs Solutions</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: #f5f5f5;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .login-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            overflow: hidden;
            width: 100%;
            max-width: 450px;
            border: 1px solid #e0e0e0;
        }

        .login-header {
            background: white;
            padding: 40px 40px 20px;
            text-align: center;
            border-bottom: 1px solid #f0f0f0;
        }

        .logo-container {
            margin-bottom: 20px;
        }

        .logo {
            width: 120px;
            height: 60px;
            margin: 0 auto 15px;
            display: block;
        }

        .company-name {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
            letter-spacing: -0.5px;
        }

        .login-subtitle {
            font-size: 16px;
            color: #666;
            font-weight: normal;
        }

        .login-form {
            padding: 30px 40px 40px;
            background: white;
        }

        .alert {
            padding: 12px 16px;
            border-radius: 6px;
            margin-bottom: 20px;
            font-size: 14px;
            border: 1px solid;
        }

        .alert-error {
            background: #fef2f2;
            color: #dc2626;
            border-color: #fecaca;
        }

        .alert-success {
            background: #f0fdf4;
            color: #16a34a;
            border-color: #bbf7d0;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 6px;
            font-weight: normal;
            color: #333;
            font-size: 14px;
        }

        .form-group input {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 16px;
            transition: all 0.2s ease;
            background: white;
            color: #333;
        }

        .form-group input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .form-group input::placeholder {
            color: #9ca3af;
        }

        .btn {
            width: 100%;
            padding: 12px 24px;
            background: #374151;
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            font-weight: normal;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-top: 10px;
        }

        .btn:hover {
            background: #1f2937;
        }

        .btn:active {
            transform: translateY(1px);
        }

        .login-footer {
            background: #f9fafb;
            padding: 20px 40px;
            border-top: 1px solid #f0f0f0;
            font-size: 13px;
            color: #6b7280;
            text-align: center;
        }

        .demo-credentials {
            margin-top: 12px;
            padding: 12px;
            background: white;
            border-radius: 6px;
            border: 1px solid #e5e7eb;
            font-size: 12px;
        }

        .demo-credentials p {
            margin: 4px 0;
            color: #374151;
        }

        .demo-credentials strong {
            color: #1f2937;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <div class="logo-container">
                <img src="assets/img/logo.png" alt="ACS Logo" class="logo">
            </div>
            <div class="company-name">Welcome to Advanced Customs Solutions</div>
            <div class="login-subtitle">Please enter your login details</div>
        </div>

        <div class="login-form">
            <?php if ($error_message): ?>
                <div class="alert alert-error">
                    <?php echo htmlspecialchars($error_message); ?>
                </div>
            <?php endif; ?>

            <?php if ($success_message): ?>
                <div class="alert alert-success">
                    <?php echo htmlspecialchars($success_message); ?>
                </div>
            <?php endif; ?>

            <form method="POST" action="login.php">
                <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">

                <div class="form-group">
                    <label for="email">User name or email:</label>
                    <input type="email" id="email" name="email" required
                           value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>"
                           placeholder="">
                </div>

                <div class="form-group">
                    <label for="password">Password:</label>
                    <input type="password" id="password" name="password" required
                           placeholder="password">
                </div>

                <button type="submit" class="btn">Log in</button>
            </form>
        </div>

        <div class="login-footer">
            <p><strong>Demo Credentials:</strong></p>
            <div class="demo-credentials">
                <p><strong>Admin:</strong> <EMAIL> / admin123</p>
                <p><strong>User:</strong> <EMAIL> / admin123</p>
            </div>
        </div>
    </div>
</body>
</html>
